import os
import logging
import time
import datetime
import sqlite3
import pandas as pd
import numpy as np
from alpaca.trading.client import TradingClient
from alpaca.trading.requests import MarketOrderRequest, LimitOrderRequest
from alpaca.trading.enums import OrderSide, TimeInForce
from alpaca.data.historical import StockHistoricalDataClient
from alpaca.data.requests import StockBarsRequest
from alpaca.data.timeframe import TimeFrame
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import precision_score
import talib

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load Alpaca credentials from environment variables
API_KEY = os.getenv('ALPACA_API_KEY')
API_SECRET = os.getenv('ALPACA_API_SECRET')
if not API_KEY or not API_SECRET:
    raise ValueError("ALPACA_API_KEY and ALPACA_API_SECRET must be set in environment variables")

# Initialize Alpaca clients for paper trading
trading_client = TradingClient(API_KEY, API_SECRET, paper=True)
data_client = StockHistoricalDataClient(API_KEY, API_SECRET)

# Stock symbols for diversified portfolio
STOCKS = ['AAPL', 'TSLA', 'GOOGL']

# Strategy parameters
PROFIT_TARGET_MIN = 0.001  # 0.1%
PROFIT_TARGET_MAX = 0.005  # 0.5%
STOP_LOSS_PCT = 0.005  # 0.5%
TIMEFRAME_MINUTES = 1  # 1-minute bars
PORTFOLIO_SIZE = len(STOCKS)
ALLOCATION_PCT = 1.0 / PORTFOLIO_SIZE  # Equal allocation

# ML parameters
ML_FEATURES = ['rsi', 'macd', 'macd_signal', 'bollinger_upper', 'bollinger_middle', 'bollinger_lower']
ML_TARGET = 'price_up'  # Binary: 1 if next bar close > current close
ML_MODEL = RandomForestClassifier(n_estimators=100, min_samples_split=50, random_state=42)

# SQLite database setup
DB_FILE = 'trading_data.db'
conn = sqlite3.connect(DB_FILE)
cursor = conn.cursor()

# Create tables if not exist
cursor.execute('''
CREATE TABLE IF NOT EXISTS trades (
    id INTEGER PRIMARY KEY,
    timestamp DATETIME,
    symbol TEXT,
    side TEXT,
    qty REAL,
    price REAL,
    profit REAL
)
''')
cursor.execute('''
CREATE TABLE IF NOT EXISTS market_data (
    timestamp DATETIME,
    symbol TEXT,
    open REAL,
    high REAL,
    low REAL,
    close REAL,
    volume INTEGER
)
''')
conn.commit()

def fetch_bars(symbol, start, end, timeframe=TimeFrame.Minute):
    """Fetch historical bars for a symbol."""
    try:
        request = StockBarsRequest(
            symbol_or_symbols=symbol,
            start=start,
            end=end,
            timeframe=timeframe
        )
        bars = data_client.get_stock_bars(request)
        df = bars.df.reset_index()
        df['timestamp'] = df['timestamp'].dt.tz_localize(None)  # Remove timezone for SQLite
        return df
    except Exception as e:
        logger.error(f"Error fetching bars for {symbol}: {e}")
        return pd.DataFrame()

def save_market_data(df, symbol):
    """Save market data to SQLite."""
    try:
        data_tuples = [
            (row['timestamp'], symbol, row['open'], row['high'], row['low'], row['close'], row['volume'])
            for _, row in df.iterrows()
        ]
        cursor.executemany('''
        INSERT OR REPLACE INTO market_data (timestamp, symbol, open, high, low, close, volume)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', data_tuples)
        conn.commit()
    except Exception as e:
        logger.error(f"Error saving market data for {symbol}: {e}")

def log_trade(timestamp, symbol, side, qty, price, profit=None):
    """Log trade to SQLite."""
    try:
        cursor.execute('''
        INSERT INTO trades (timestamp, symbol, side, qty, price, profit)
        VALUES (?, ?, ?, ?, ?, ?)
        ''', (timestamp, symbol, side, qty, price, profit))
        conn.commit()
    except Exception as e:
        logger.error(f"Error logging trade: {e}")

def calculate_indicators(df):
    """Calculate technical indicators using TA-Lib."""
    close = np.array(df['close'])
    df['sma'] = talib.SMA(close, timeperiod=20)
    df['ema_fast'] = talib.EMA(close, timeperiod=12)
    df['ema_slow'] = talib.EMA(close, timeperiod=26)
    df['rsi'] = talib.RSI(close, timeperiod=14)
    df['macd'], df['macd_signal'], _ = talib.MACD(close, fastperiod=12, slowperiod=26, signalperiod=9)
    df['bollinger_upper'], df['bollinger_middle'], df['bollinger_lower'] = talib.BBANDS(close, timeperiod=20, nbdevup=2, nbdevdn=2)
    return df

def get_current_positions():
    """Get current positions."""
    try:
        return trading_client.get_all_positions()
    except Exception as e:
        logger.error(f"Error getting positions: {e}")
        return []

def rebalance_portfolio():
    """Rebalance to maintain equal allocation across stocks."""
    try:
        account = trading_client.get_account()
        equity = float(account.equity)
        target_allocation = equity * ALLOCATION_PCT

        positions = {pos.symbol: float(pos.market_value) for pos in get_current_positions()}

        for symbol in STOCKS:
            current_value = positions.get(symbol, 0)
            difference = target_allocation - current_value

            if abs(difference) > equity * 0.01:  # Rebalance if >1% off
                qty = abs(difference) / get_latest_price(symbol)
                side = OrderSide.BUY if difference > 0 else OrderSide.SELL

                order_data = MarketOrderRequest(
                    symbol=symbol,
                    qty=qty,
                    side=side,
                    time_in_force=TimeInForce.DAY
                )
                order = trading_client.submit_order(order_data)
                logger.info(f"Rebalanced {symbol}: {side} {qty} shares")
    except Exception as e:
        logger.error(f"Error rebalancing portfolio: {e}")

def get_latest_price(symbol):
    """Get latest close price."""
    now = datetime.datetime.now()
    start = now - datetime.timedelta(minutes=5)
    bars = fetch_bars(symbol, start, now)
    if not bars.empty:
        return bars.iloc[-1]['close']
    return None

def train_ml_model():
    """Train ML model daily using historical data."""
    try:
        # Fetch last 1 year data for all stocks
        end = datetime.datetime.now()
        start = end - datetime.timedelta(days=365)
        all_data = pd.DataFrame()
        for symbol in STOCKS:
            df = fetch_bars(symbol, start, end, TimeFrame.Day)  # Daily for training
            df['symbol'] = symbol
            all_data = pd.concat([all_data, df])

        # Feature engineering
        all_data = calculate_indicators(all_data)
        all_data['price_up'] = (all_data['close'].shift(-1) > all_data['close']).astype(int)
        all_data.dropna(inplace=True)

        # Train/test split (80/20)
        train_size = int(len(all_data) * 0.8)
        train = all_data.iloc[:train_size]
        test = all_data.iloc[train_size:]

        model = ML_MODEL
        model.fit(train[ML_FEATURES], train[ML_TARGET])

        # Evaluate
        preds = model.predict(test[ML_FEATURES])
        score = precision_score(test[ML_TARGET], preds)
        logger.info(f"ML model trained with precision: {score}")

        return model
    except Exception as e:
        logger.error(f"Error training ML model: {e}")
        return None

def scalping_strategy(symbol, df, model):
    """Implement scalping logic with ML filter."""
    try:
        latest = df.iloc[-1]
        positions = [pos for pos in get_current_positions() if pos.symbol == symbol]
        has_position = bool(positions)

        # Use ML to predict if price will go up
        features = latest[ML_FEATURES].values.reshape(1, -1)
        pred_prob = model.predict_proba(features)[0][1]  # Prob of price_up

        # Buy signal: EMA crossover + RSI < 30 + ML prob > 0.6
        if latest['ema_fast'] > latest['ema_slow'] and latest['rsi'] < 30 and pred_prob > 0.6 and not has_position:
            qty = (float(trading_client.get_account().cash) * ALLOCATION_PCT) / latest['close']
            order_data = MarketOrderRequest(
                symbol=symbol,
                qty=qty,
                side=OrderSide.BUY,
                time_in_force=TimeInForce.DAY
            )
            buy_order = trading_client.submit_order(order_data)
            buy_price = float(buy_order.filled_avg_price) if buy_order.filled_avg_price else latest['close']
            log_trade(datetime.datetime.now(), symbol, 'BUY', qty, buy_price)

            # Set limit sell for profit target
            profit_target = np.random.uniform(PROFIT_TARGET_MIN, PROFIT_TARGET_MAX)
            sell_price = buy_price * (1 + profit_target)
            limit_order_data = LimitOrderRequest(
                symbol=symbol,
                qty=qty,
                side=OrderSide.SELL,
                time_in_force=TimeInForce.DAY,
                limit_price=sell_price
            )
            trading_client.submit_order(limit_order_data)

            # Set stop loss
            stop_price = buy_price * (1 - STOP_LOSS_PCT)
            stop_order_data = LimitOrderRequest(
                symbol=symbol,
                qty=qty,
                side=OrderSide.SELL,
                time_in_force=TimeInForce.DAY,
                limit_price=stop_price
            )
            trading_client.submit_order(stop_order_data)
            logger.info(f"Scalp buy {symbol} at {buy_price}, target {sell_price}")

        # Sell if profit hit or stop loss, but handled by orders

    except Exception as e:
        logger.error(f"Error in scalping strategy for {symbol}: {e}")

def monitor_performance():
    """Monitor and log performance."""
    try:
        account = trading_client.get_account()
        pnl = float(account.equity) - float(account.last_equity)
        logger.info(f"Current PNL: {pnl}")
        # Additional metrics can be queried from DB
    except Exception as e:
        logger.error(f"Error monitoring performance: {e}")

def main_loop():
    """Main trading loop."""
    model = None  # Initial model
    while True:
        now = datetime.datetime.now()
        market_open = now.hour >= 9 and now.hour < 16 and now.weekday() < 5  # Approx NYSE hours

        if market_open:
            end = now
            start = end - datetime.timedelta(minutes=60)  # Last hour data
            for symbol in STOCKS:
                df = fetch_bars(symbol, start, end)
                if not df.empty:
                    save_market_data(df, symbol)
                    df = calculate_indicators(df)
                    scalping_strategy(symbol, df, model)
            rebalance_portfolio()
            monitor_performance()
        else:
            # After close, retrain model
            if now.hour == 16 and now.minute == 0:  # Approx after close
                model = train_ml_model()

        time.sleep(60 * TIMEFRAME_MINUTES)  # Check every timeframe

if __name__ == "__main__":
    try:
        main_loop()
    except KeyboardInterrupt:
        logger.info("Shutting down...")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
    finally:
        conn.close()